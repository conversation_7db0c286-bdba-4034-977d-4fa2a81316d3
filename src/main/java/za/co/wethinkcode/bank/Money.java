package za.co.wethinkcode.bank;

public class Money implements Comparable<Money> {
    public static final Money ZERO = new Money( 0 );
    private final long amountInCents;
    
    public Money( long amountInCents ){
        this.amountInCents = amountInCents;
    }

    public Money add(Money amount){
        return new Money( this.amountInCents + amount.amountInCents );
    }

    public Money subtract(Money amount){
        return new Money( this.amountInCents - amount.amountInCents );
    }

    public String formattedAsRands(){
        return "R" + String.format("%.2f", amountInCents / 100.0);
    }


    @Override
    public int compareTo(Money money) {
        return 0;
    }
    @Override
    public boolean equals(Object obj) {
        return false;
    }
    @Override
    public int hashCode() {
        return 0;
    }
}

