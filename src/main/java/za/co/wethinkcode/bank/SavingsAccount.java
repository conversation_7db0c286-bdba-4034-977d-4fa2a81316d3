package za.co.wethinkcode.bank;

public class SavingsAccount extends Account{

    private final Money MINBALANCE = Money.ZERO;
    private Money availableBalance = Money.ZERO;
    private String accountName;

    public SavingsAccount() {
        accountName = "Savings Account";
    }

    public SavingsAccount(String accountName) {
        this.accountName = accountName;
    }

    @Override
    public Money getAvailableBalance() {
        return availableBalance;
    }

    @Override
    public String accountName() {
        return accountName;
    }

    @Override
    public void renameAccount(String accountName) {
        this.accountName = accountName;
    }

    @Override
    public void creditAccount(Money creditAmount) {
        availableBalance += creditAmount;
    }

    @Override
    public void debitAccount(Money debitAmount) {
        if (!insufficientFunds(debitAmount)) availableBalance -= debitAmount;
        else System.out.println("Insufficient Funds");
    }

    private boolean insufficientFunds(Money debitAmount) {
        return (availableBalance - debitAmount) < MINBALANCE;
    }
}
